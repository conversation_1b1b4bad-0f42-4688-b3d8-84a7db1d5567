import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  getCurrentUser,
  updateCurrentUser,
  uploadProfileImage,
  reset,
} from "../../redux/slices/authSlice";
import SellerLayout from "../../components/seller/SellerLayout";
import { FaUser, FaEnvelope, FaPhone, FaEdit, FaFacebook, FaLinkedin, FaTwitter, FaDollarSign, FaGraduationCap, FaTrash, FaPlus, FaInstagram } from "react-icons/fa";
import { getImageUrl } from "../../utils/constants";
import toast from "../../utils/toast";
import "../../styles/SellerProfile.css";
import { FaXTwitter } from "react-icons/fa6";



const SellerProfile = () => {
  const dispatch = useDispatch();
  const { user, isLoading, isSuccess, isError, error } = useSelector(
    (state) => state.auth
  );

  // Local state for form inputs
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    mobile: "",
    profileImage: "",
    // Onboarding details
    description: "",
    experiences: [],
    minTrainingCost: "",
    socialLinks: {
      facebook: "",
      linkedin: "",
      twitter: "",
    },
    sellerProfilePic: "", // Track sellerInfo.profilePic separately
    isOnboardingComplete: false, // Track onboarding completion status
  });

  const [selectedFile, setSelectedFile] = useState(null);
  const [previewImage, setPreviewImage] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);

  // State for validation errors
  const [validationErrors, setValidationErrors] = useState({
    experienceYears: {} // Track year validation errors for each experience
  });

  // Fetch user data on component mount
  useEffect(() => {
    dispatch(getCurrentUser());
  }, [dispatch]);

  // Update form data when user data is loaded
  useEffect(() => {
    if (user && user.data) {
      const userData = user.data;
      const sellerInfo = userData.sellerInfo || {};

      setFormData({
        firstName: userData.firstName || "",
        lastName: userData.lastName || "",
        email: userData.email || "",
        mobile: userData.mobile || "",
        profileImage: sellerInfo.profilePic || "",
        // Onboarding details
        description: sellerInfo.description || "",
        experiences: sellerInfo.experiences && sellerInfo.experiences.length > 0
          ? sellerInfo.experiences
          : [{ schoolName: '', position: '', fromYear: '', toYear: '' }],
        minTrainingCost: sellerInfo.minTrainingCost || "",
        socialLinks: {
          facebook: sellerInfo.socialLinks?.facebook || "",
          linkedin: sellerInfo.socialLinks?.linkedin || "",
          twitter: sellerInfo.socialLinks?.twitter || "",
        },
        sellerProfilePic: sellerInfo.profilePic || userData.profileImage || "",
        isOnboardingComplete: sellerInfo.isOnboardingComplete || false,
      });
    } else if (user) {
      const sellerInfo = user.sellerInfo || {};

      setFormData({
        firstName: user.firstName || "",
        lastName: user.lastName || "",
        email: user.email || "",
        mobile: user.mobile || "",
        profileImage: sellerInfo.profilePic || "",
        // Onboarding details
        description: sellerInfo.description || "",
        experiences: sellerInfo.experiences && sellerInfo.experiences.length > 0
          ? sellerInfo.experiences
          : [{ schoolName: '', position: '', fromYear: '', toYear: '' }],
        minTrainingCost: sellerInfo.minTrainingCost || "",
        socialLinks: {
          facebook: sellerInfo.socialLinks?.facebook || "",
          linkedin: sellerInfo.socialLinks?.linkedin || "",
          twitter: sellerInfo.socialLinks?.twitter || "",
        },
        sellerProfilePic: sellerInfo.profilePic || user.profileImage || "",
        isOnboardingComplete: sellerInfo.isOnboardingComplete || false,
      });
    }
  }, [user]);

  // Handle success/error states - only for form submission
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [imageError, setImageError] = useState(false);

  useEffect(() => {
    if (isSubmitting && isSuccess && !isLoading) {
      toast.success("Profile updated successfully!");
      dispatch(reset());
      setIsSubmitting(false);
      // Auto-exit edit mode after successful update
      setIsEditMode(false);
      // Refresh user data to get updated profile image
      dispatch(getCurrentUser());
    }

    if (isSubmitting && isError && error) {
      toast.error(error.message || "Failed to update profile");
      dispatch(reset());
      setIsSubmitting(false);
    }
  }, [isSuccess, isError, error, isLoading, dispatch, isSubmitting]);

  // Validate experience years
  const validateExperienceYears = () => {
    const currentYear = new Date().getFullYear();
    const minYear = 1950;
    const maxYear = currentYear;
    const yearErrors = {};
    let hasErrors = false;

    console.log('=== Seller Profile Year Validation Debug ===');
    console.log('Current year:', currentYear);
    console.log('Max allowed year:', maxYear);
    console.log('Experiences to validate:', formData.experiences);

    formData.experiences.forEach((exp, index) => {
      const fromYear = parseInt(exp.fromYear);
      const toYear = parseInt(exp.toYear);
      const expErrors = {};

      console.log(`Experience ${index}:`, {
        fromYearString: exp.fromYear,
        toYearString: exp.toYear,
        fromYearParsed: fromYear,
        toYearParsed: toYear
      });

      // Validate From Year
      const fromYearStr = String(exp.fromYear || '').trim();
      if (!fromYearStr) {
        expErrors.fromYear = 'From year is required';
        hasErrors = true;
        console.log(`From year error: required`);
      } else if (isNaN(fromYear) || fromYear < minYear || fromYear > maxYear) {
        expErrors.fromYear = `From year must be between ${minYear} and ${maxYear}`;
        hasErrors = true;
        console.log(`From year error: out of range (${fromYear})`);
      }

      // Validate To Year
      const toYearStr = String(exp.toYear || '').trim();
      if (!toYearStr) {
        expErrors.toYear = 'To year is required';
        hasErrors = true;
        console.log(`To year error: required`);
      } else if (isNaN(toYear) || toYear < minYear || toYear > maxYear) {
        expErrors.toYear = `To year must be between ${minYear} and ${maxYear}`;
        hasErrors = true;
        console.log(`To year error: out of range (${toYear})`);
      } else if (!isNaN(fromYear) && toYear <= fromYear) {
        expErrors.toYear = 'To year must be greater than from year';
        hasErrors = true;
        console.log(`To year error: less than from year`);
      }

      if (Object.keys(expErrors).length > 0) {
        yearErrors[index] = expErrors;
        console.log(`Errors for experience ${index}:`, expErrors);
      }
    });

    console.log('Total year errors:', yearErrors);
    console.log('Has errors:', hasErrors);

    if (hasErrors) {
      setValidationErrors(prev => ({
        ...prev,
        experienceYears: yearErrors
      }));
      return false;
    }

    // Clear year errors if validation passes
    setValidationErrors(prev => ({
      ...prev,
      experienceYears: {}
    }));
    return true;
  };

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;

    // Handle nested social links
    if (name.startsWith('socialLinks.')) {
      const socialField = name.split('.')[1];
      setFormData((prev) => ({
        ...prev,
        socialLinks: {
          ...prev.socialLinks,
          [socialField]: value,
        },
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  // Handle experience changes
  const handleExperienceChange = (index, field, value) => {
    setFormData((prev) => ({
      ...prev,
      experiences: prev.experiences.map((exp, idx) =>
        idx === index ? { ...exp, [field]: value } : exp
      ),
    }));

    // Clear year validation errors when user starts typing
    if (field === 'fromYear' || field === 'toYear') {
      if (validationErrors.experienceYears?.[index]?.[field]) {
        setValidationErrors(prev => ({
          ...prev,
          experienceYears: {
            ...prev.experienceYears,
            [index]: {
              ...prev.experienceYears[index],
              [field]: ''
            }
          }
        }));
      }
    }
  };

  // Add new experience
  const addExperience = () => {
    setFormData((prev) => ({
      ...prev,
      experiences: [
        ...prev.experiences,
        { schoolName: '', position: '', fromYear: '', toYear: '' },
      ],
    }));
  };

  // Remove experience
  const removeExperience = (index) => {
    if (formData.experiences.length > 1) {
      setFormData((prev) => ({
        ...prev,
        experiences: prev.experiences.filter((_, idx) => idx !== index),
      }));
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate experience years before submission
    console.log('Validating experience years before submission...');
    const yearValidationPassed = validateExperienceYears();
    if (!yearValidationPassed) {
      console.log('Year validation failed, preventing submission');
      //toast.error("Please fix the experience year validation errors before saving.");
      return;
    }

    setIsSubmitting(true);

    try {
      let profileImageUrl = formData.profileImage;

      // Upload image first if a new file is selected
      if (selectedFile) {
        const uploadResult = await dispatch(
          uploadProfileImage(selectedFile)
        ).unwrap();
        profileImageUrl = uploadResult.data.fileUrl;
      }

      // Determine the profile image URL for sellerInfo.profilePic
      // Use the new uploaded image URL, or preserve the existing sellerInfo.profilePic, or fall back to profileImage
      const sellerProfilePicUrl = selectedFile
        ? profileImageUrl  // New image uploaded
        : (formData.sellerProfilePic || profileImageUrl); // Preserve existing or use current profileImage

      // Send all editable fields including onboarding data
      const updateData = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        profileImage: profileImageUrl,
        // Include onboarding data with preserved/updated profilePic and onboarding status
        sellerInfo: {
          description: formData.description,
          experiences: formData.experiences,
          minTrainingCost: formData.minTrainingCost,
          socialLinks: formData.socialLinks,
          profilePic: sellerProfilePicUrl, // Ensure profilePic is always included
          isOnboardingComplete: formData.isOnboardingComplete, // Preserve onboarding completion status
        },
      };

      console.log('Updating seller profile with data:', updateData);
      console.log('Profile update details:', {
        profileImage: profileImageUrl,
        sellerInfoProfilePic: sellerProfilePicUrl,
        existingSellerProfilePic: formData.sellerProfilePic,
        isOnboardingComplete: formData.isOnboardingComplete,
        newImageUploaded: !!selectedFile
      });

      dispatch(updateCurrentUser(updateData));
    } catch (error) {
      console.error('Profile update error:', error);
      toast.error("Failed to upload image or update profile");
      setIsSubmitting(false);
    }
  };

  // Handle profile image upload
  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      setSelectedFile(file);
      setImageError(false); // Reset image error when new file is selected

      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewImage(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle image load error
  const handleImageError = () => {
    setImageError(true);
  };

  // Handle account deletion
  const handleDeleteAccount = () => {
    // Show confirmation dialog and handle account deletion
    if (
      window.confirm(
        "Are you sure you want to delete your account? This action cannot be undone."
      )
    ) {
      // Handle account deletion logic here
    }
  };

  return (
    <SellerLayout>
      <div className="SellerProfile">
        {/* Header Section */}
        <div className="SellerProfile__header">
          <h2 className="SellerProfile__title">Seller Profile</h2>
          <button
            className="SellerProfile__edit-btn"
            onClick={() => setIsEditMode(!isEditMode)}
          >
            <FaEdit /> {isEditMode ? "Cancel Edit" : "Edit Profile"}
          </button>
        </div>

        <div className="SellerProfile__container">
          {/* Left Section - Profile Information */}
          <div className="SellerProfile__left-section">
            {/* Basic Information */}
            <div className="SellerProfile__section">
              <h3 className="SellerProfile__section-title">Basic Information</h3>
              <div className="SellerProfile__form-row">
                <div className="SellerProfile__input-field">
                  <div className="SellerProfile__input-container">
                    <div className="SellerProfile__input-icon">
                      <FaUser />
                    </div>
                    <input
                      type="text"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleInputChange}
                      placeholder="First Name"
                      disabled={!isEditMode}
                      className={`SellerProfile__input ${!isEditMode ? 'SellerProfile__input--disabled' : ''}`}
                    />
                  </div>
                </div>

                <div className="SellerProfile__input-field">
                  <div className="SellerProfile__input-container">
                    <div className="SellerProfile__input-icon">
                      <FaUser />
                    </div>
                    <input
                      type="text"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      placeholder="Last Name"
                      disabled={!isEditMode}
                      className={`SellerProfile__input ${!isEditMode ? 'SellerProfile__input--disabled' : ''}`}
                    />
                  </div>
                </div>
              </div>

              <div className="SellerProfile__form-row ">
                <div className="SellerProfile__input-field">
                  <div className="SellerProfile__input-container">
                    <div className="SellerProfile__input-icon">
                      <FaEnvelope />
                    </div>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      placeholder="Email Address"
                      disabled
                      className="SellerProfile__input SellerProfile__input--disabled"
                    />
                  </div>
                </div>

                <div className="SellerProfile__input-field">
                  <div className="SellerProfile__input-container">
                    <div className="SellerProfile__input-icon">
                      <FaPhone />
                    </div>
                    <input
                      type="tel"
                      name="mobile"
                      value={formData.mobile}
                      placeholder="Mobile Number"
                      disabled
                      className="SellerProfile__input SellerProfile__input--disabled"
                    />
                  </div>
                </div>
              </div>
            </div>


          </div>

          {/* Right Section - Profile Image */}
          <div className="SellerProfile__right-section">
            <div className="SellerProfile__image-container">
              <h3 className="SellerProfile__image-title">Profile Image</h3>
              <div className="SellerProfile__image">
                {previewImage || (formData.profileImage && !imageError) ? (
                  <img
                    src={previewImage || getImageUrl(formData.profileImage)}
                    alt="Profile"
                    onError={handleImageError}
                  />
                ) : (
                  <div className="SellerProfile__placeholder">
                    {formData.firstName && formData.lastName ? (
                      `${formData.firstName.charAt(0)}${formData.lastName.charAt(0)}`
                    ) : (
                      <FaUser className="SellerProfile__user-icon" />
                    )}
                  </div>
                )}
              </div>
              {isEditMode && (
                <>
                  <button
                    className="SellerProfile__upload-btn"
                    onClick={() => document.getElementById("profile-image-upload").click()}
                  >
                    Upload Photo
                  </button>
                  <input
                    type="file"
                    id="profile-image-upload"
                    accept="image/*"
                    onChange={handleImageUpload}
                    style={{ display: "none" }}
                  />
                </>
              )}
            </div>
          </div>
        </div>
       <div className="SellerProfile__description-experience-container">
         {/* Description Section */}
         <div className="SellerProfile__section">
          <h3 className="SellerProfile__section-title">Description</h3>
          <div className="SellerProfile__description-container">
            {isEditMode ? (
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Enter your professional description..."
                className="SellerProfile__textarea"
                rows={4}
              />
            ) : (
              <div className="SellerProfile__description-display">
                {formData.description || "No description provided"}
              </div>
            )}
          </div>


        </div>
        {/* Training Cost Section */}
        <div className="SellerProfile__section">
          <h3 className="SellerProfile__section-title">Minimum Training Cost</h3>
          <div className="SellerProfile__cost-container">
            {isEditMode ? (
              <div className="SellerProfile__input-field">
                <div className="SellerProfile__input-container">
                  <div className="SellerProfile__input-icon">
                    <FaDollarSign />
                  </div>
                  <input
                    type="number"
                    name="minTrainingCost"
                    value={formData.minTrainingCost}
                    onChange={handleInputChange}
                    placeholder="Enter amount"
                    className="SellerProfile__input"
                  />
                </div>
              </div>
            ) : (
              <div className="SellerProfile__cost-display">
                <FaDollarSign className="SellerProfile__cost-icon" />
                <span className="SellerProfile__cost-amount">
                  {formData.minTrainingCost ? `${formData.minTrainingCost}` : "Not specified"}
                </span>
              </div>
            )}
          </div>
        </div>
       </div>

        {/* Experience Section */}
        <div className="SellerProfile__section">
          <h3 className="SellerProfile__section-title">Experience</h3>
          <div className="SellerProfile__experiences">
            {isEditMode ? (
              <>
                {formData.experiences && formData.experiences.length > 0 ? (
                  <div className="SellerProfile__experiences-grid">
                    {formData.experiences.map((exp, index) => (
                      <div key={index} className="SellerProfile__experience-edit-item">
                        <div className="SellerProfile__experience-edit-header">
                          <FaGraduationCap className="SellerProfile__experience-icon" />
                          <span className="SellerProfile__experience-number">Experience {index + 1}</span>
                          {formData.experiences.length > 1 && (
                            <button
                              type="button"
                              className="SellerProfile__remove-btn"
                              onClick={() => removeExperience(index)}
                            >
                              <FaTrash />
                            </button>
                          )}
                        </div>
                        <div className="SellerProfile__experience-form">
                          <div className="SellerProfile__form-row">
                            <div className="SellerProfile__input-field">
                              <input
                                type="text"
                                placeholder="Enter School Name"
                                value={exp.schoolName}
                                onChange={(e) => handleExperienceChange(index, 'schoolName', e.target.value)}
                                className="SellerProfile__input"
                              />
                            </div>
                            <div className="SellerProfile__input-field">
                              <input
                                type="text"
                                placeholder="Enter Position"
                                value={exp.position}
                                onChange={(e) => handleExperienceChange(index, 'position', e.target.value)}
                                className="SellerProfile__input"
                              />
                            </div>
                          </div>
                          <div className="SellerProfile__form-row SellerProfile__form-row-email-phone">
                            <div className="SellerProfile__input-field">
                              <input
                                type="text"
                                placeholder="From Year"
                                value={exp.fromYear}
                                onChange={(e) => handleExperienceChange(index, 'fromYear', e.target.value)}
                                onBlur={() => validateExperienceYears()}
                                className={`SellerProfile__input ${validationErrors.experienceYears?.[index]?.fromYear ? 'SellerProfile__input--error' : ''}`}
                              />
                              {validationErrors.experienceYears?.[index]?.fromYear && (
                                <div className="SellerProfile__field-error">
                                  {validationErrors.experienceYears[index].fromYear}
                                </div>
                              )}
                            </div>

                            <div className="SellerProfile__input-field">
                              <input
                                type="text"
                                placeholder="To Year"
                                value={exp.toYear}
                                onChange={(e) => handleExperienceChange(index, 'toYear', e.target.value)}
                                onBlur={() => validateExperienceYears()}
                                className={`SellerProfile__input ${validationErrors.experienceYears?.[index]?.toYear ? 'SellerProfile__input--error' : ''}`}
                              />
                              {validationErrors.experienceYears?.[index]?.toYear && (
                                <div className="SellerProfile__field-error">
                                  {validationErrors.experienceYears[index].toYear}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="SellerProfile__no-data">No experience information provided</div>
                )}
                <div className="SellerProfile__add-experience-container">
                  <button
                    type="button"
                    className="SellerProfile__add-btn"
                    onClick={addExperience}
                  >
                    <FaPlus /> Add More Experience
                  </button>
                </div>
              </>
            ) : (
              <>
                {formData.experiences && formData.experiences.length > 0 ? (
                  <div className="SellerProfile__experiences-grid">
                    {formData.experiences.map((exp, index) => (
                      <div key={index} className="SellerProfile__experience-item">
                        <div className="SellerProfile__experience-header">
                          <FaGraduationCap className="SellerProfile__experience-icon" />
                          <span className="SellerProfile__experience-number">Experience {index + 1}</span>
                        </div>
                        <div className="SellerProfile__experience-content">
                          <div className="SellerProfile__experience-field">{exp.schoolName || "School Name"}</div>
                          <div className="SellerProfile__experience-field">{exp.position || "Position"}</div>
                          <div className="SellerProfile__experience-years">
                            <span>{exp.fromYear || "Start Year"}</span>
                            <span>{exp.toYear || "End Year"}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="SellerProfile__no-data">No experience information provided</div>
                )}
              </>
            )}
          </div>
        </div>



        {/* Social Links Section */}
        <div className="SellerProfile__section">
          <h3 className="SellerProfile__section-title">Social Media Links</h3>
          <div className="SellerProfile__social-links">
            {isEditMode ? (
              <>
                <div className="SellerProfile__input-field">
                  <div className="SellerProfile__input-container">
                    <div className="SellerProfile__input-icon">
                      <FaFacebook className="facebook" />
                    </div>
                    <input
                      type="url"
                      name="socialLinks.facebook"
                      value={formData.socialLinks.facebook}
                      onChange={handleInputChange}
                      placeholder="Facebook URL"
                      className="SellerProfile__input"
                    />
                  </div>
                </div>

                <div className="SellerProfile__input-field">
                  <div className="SellerProfile__input-container">
                    <div className="SellerProfile__input-icon">
                      <FaInstagram className="linkedin" />
                    </div>
                    <input
                      type="url"
                      name="socialLinks.linkedin"
                      value={formData.socialLinks.linkedin}
                      onChange={handleInputChange}
                      placeholder="LinkedIn URL"
                      className="SellerProfile__input"
                    />
                  </div>
                </div>

                <div className="SellerProfile__input-field">
                  <div className="SellerProfile__input-container">
                    <div className="SellerProfile__input-icon">
                      <FaXTwitter className="twitter" />
                    </div>
                    <input
                      type="url"
                      name="socialLinks.twitter"
                      value={formData.socialLinks.twitter}
                      onChange={handleInputChange}
                      placeholder="Twitter URL"
                      className="SellerProfile__input"
                    />
                  </div>
                </div>
              </>
            ) : (
              <>
                <div className="SellerProfile__social-item">
                  <FaFacebook className="SellerProfile__social-icon facebook" />
                  <span className="SellerProfile__social-label">Facebook:</span>
                  <span className="SellerProfile__social-value">
                    {formData.socialLinks.facebook ? (
                      <a href={formData.socialLinks.facebook} target="_blank" rel="noopener noreferrer">
                        {formData.socialLinks.facebook}
                      </a>
                    ) : (
                      "Not provided"
                    )}
                  </span>
                </div>

                <div className="SellerProfile__social-item">
                  <FaInstagram className="SellerProfile__social-icon linkedin" />
                  <span className="SellerProfile__social-label">Instagram:</span>
                  <span className="SellerProfile__social-value">
                    {formData.socialLinks.linkedin ? (
                      <a href={formData.socialLinks.linkedin} target="_blank" rel="noopener noreferrer">
                        {formData.socialLinks.linkedin}
                      </a>
                    ) : (
                      "Not provided"
                    )}
                  </span>
                </div>

                <div className="SellerProfile__social-item">
                  <FaXTwitter className="SellerProfile__social-icon twitter" />
                  <span className="SellerProfile__social-label">Twitter:</span>
                  <span className="SellerProfile__social-value">
                    {formData.socialLinks.twitter ? (
                      <a href={formData.socialLinks.twitter} target="_blank" rel="noopener noreferrer">
                        {formData.socialLinks.twitter}
                      </a>
                    ) : (
                      "Not provided"
                    )}
                  </span>
                </div>
              </>
            )}
          </div>
        </div>
        {/* Action Buttons */}
        {isEditMode && (
          <div className="SellerProfile__buttons">
            <button
              type="button"
              className="SellerProfile__delete-btn"
              onClick={handleDeleteAccount}
            >
              Delete Account
            </button>

            <button
              type="button"
              className="SellerProfile__save-btn"
              onClick={handleSubmit}
              disabled={isSubmitting || isLoading}
            >
              {isSubmitting || isLoading ? "Updating..." : "Update & Save"}
            </button>
          </div>
        )}
      </div>
    </SellerLayout>
  );
};

export default SellerProfile;
