import React, { useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectBids,
  selectSelectedBids,
  selectUI,
  setSelectedBids,
  showBidDetailModal,
  updateBid,
  deleteBid,
  approveBid,
  rejectBid,
  addActivity,
} from "../../redux/slices/adminDashboardSlice";
import AdminLayout from "../../components/admin/AdminLayout";
import "../../styles/AdminBidManagement.css";

// Icons
import { FaGavel, FaSearch, FaFilter, FaEye, FaEdit, FaTrash, FaCheck, FaTimes } from "react-icons/fa";
import { MdAdd } from "react-icons/md";

const AdminBidManagement = () => {
  const dispatch = useDispatch();
  const bids = useSelector(selectBids);
  const selectedBids = useSelector(selectSelectedBids);
  const ui = useSelector(selectUI);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  // Filter bids based on search and filters
  const filteredBids = bids.filter(bid => {
    const matchesSearch =
      bid.bidId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      bid.contentTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||
      bid.bidderName.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === "all" || bid.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Handle select all
  const handleSelectAll = (e) => {
    if (e.target.checked) {
      dispatch(setSelectedBids(filteredBids.map(bid => bid.id)));
    } else {
      dispatch(setSelectedBids([]));
    }
  };

  // Handle individual select
  const handleSelectBid = (bidId) => {
    const newSelection = selectedBids.includes(bidId)
      ? selectedBids.filter(id => id !== bidId)
      : [...selectedBids, bidId];
    dispatch(setSelectedBids(newSelection));
  };

  // Handle bid actions
  const handleBidAction = (bidItem, action) => {
    switch (action) {
      case 'view':
      case 'edit':
        dispatch(showBidDetailModal(bidItem));
        break;
      case 'approve':
        if (window.confirm(`Approve bid "${bidItem.bidId}"?`)) {
          dispatch(approveBid(bidItem.id));
          dispatch(addActivity({
            id: Date.now(),
            type: 'bid_approval',
            description: `Bid approved: ${bidItem.bidId} for ${bidItem.contentTitle}`,
            timestamp: new Date().toISOString(),
            user: 'Admin',
          }));
          alert(`Bid "${bidItem.bidId}" has been approved!`);
        }
        break;
      case 'reject':
        const reason = prompt(`Reason for rejecting bid "${bidItem.bidId}":`);
        if (reason) {
          dispatch(rejectBid(bidItem.id));
          dispatch(addActivity({
            id: Date.now(),
            type: 'bid_rejection',
            description: `Bid rejected: ${bidItem.bidId} - Reason: ${reason}`,
            timestamp: new Date().toISOString(),
            user: 'Admin',
          }));
          alert(`Bid "${bidItem.bidId}" has been rejected.`);
        }
        break;
      case 'delete':
        if (window.confirm(`Delete bid "${bidItem.bidId}"? This action cannot be undone.`)) {
          dispatch(deleteBid(bidItem.id));
          dispatch(addActivity({
            id: Date.now(),
            type: 'bid_deletion',
            description: `Bid deleted: ${bidItem.bidId}`,
            timestamp: new Date().toISOString(),
            user: 'Admin',
          }));
          alert(`Bid "${bidItem.bidId}" has been deleted!`);
        }
        break;
      default:
        break;
    }
  };

  // Handle bulk actions
  const handleBulkAction = (action) => {
    if (selectedBids.length === 0) {
      alert('Please select bids first');
      return;
    }

    switch (action) {
      case 'approve':
        if (window.confirm(`Approve ${selectedBids.length} selected bids?`)) {
          selectedBids.forEach(id => {
            dispatch(approveBid(id));
          });
          dispatch(addActivity({
            id: Date.now(),
            type: 'bulk_bid_approval',
            description: `Bulk approved ${selectedBids.length} bids`,
            timestamp: new Date().toISOString(),
            user: 'Admin',
          }));
          alert(`${selectedBids.length} bids approved`);
          dispatch(setSelectedBids([]));
        }
        break;
      case 'reject':
        const reason = prompt(`Reason for rejecting ${selectedBids.length} bids:`);
        if (reason) {
          selectedBids.forEach(id => {
            dispatch(rejectBid(id));
          });
          dispatch(addActivity({
            id: Date.now(),
            type: 'bulk_bid_rejection',
            description: `Bulk rejected ${selectedBids.length} bids - Reason: ${reason}`,
            timestamp: new Date().toISOString(),
            user: 'Admin',
          }));
          alert(`${selectedBids.length} bids rejected`);
          dispatch(setSelectedBids([]));
        }
        break;
      case 'delete':
        if (window.confirm(`Delete ${selectedBids.length} selected bids? This action cannot be undone.`)) {
          selectedBids.forEach(id => {
            dispatch(deleteBid(id));
          });
          dispatch(addActivity({
            id: Date.now(),
            type: 'bulk_bid_deletion',
            description: `Bulk deleted ${selectedBids.length} bids`,
            timestamp: new Date().toISOString(),
            user: 'Admin',
          }));
          alert(`${selectedBids.length} bids deleted`);
          dispatch(setSelectedBids([]));
        }
        break;
      default:
        break;
    }
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Get status badge class
  const getStatusBadge = (status) => {
    switch (status) {
      case 'Active':
        return 'status-badge active';
      case 'Won':
        return 'status-badge won';
      case 'Lost':
        return 'status-badge lost';
      case 'Outbid':
        return 'status-badge outbid';
      case 'Expired':
        return 'status-badge expired';
      default:
        return 'status-badge';
    }
  };

  return (
    <AdminLayout>
      <div className="AdminBidManagement">
        {/* Header Actions */}
        <div className="AdminBidManagement__header">
          <div className="header-left">
            <div className="search-container">
              <FaSearch className="search-icon" />
              <input
                type="text"
                placeholder="Search bids by ID, content, or bidder..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="search-input"
              />
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="AdminBidManagement__filters">
          <div className="filter-group">
            <FaFilter className="filter-icon" />
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="filter-select"
            >
              <option value="all">All Status</option>
              <option value="Active">Active</option>
              <option value="Won">Won</option>
              <option value="Lost">Lost</option>
              <option value="Outbid">Outbid</option>
              <option value="Expired">Expired</option>
            </select>
          </div>

          {selectedBids.length > 0 && (
            <div className="bulk-actions">
              <span className="selected-count">
                {selectedBids.length} selected
              </span>
              <button
                className="btn btn-success"
                onClick={() => handleBulkAction('approve')}
              >
                <FaCheck />
                Approve
              </button>
              <button
                className="btn btn-warning"
                onClick={() => handleBulkAction('reject')}
              >
                <FaTimes />
                Reject
              </button>
              <button
                className="btn btn-danger"
                onClick={() => handleBulkAction('delete')}
              >
                Delete
              </button>
            </div>
          )}
        </div>

        {/* Bids Table */}
        <div className="AdminBidManagement__table">
          <div className="table-container">
            <table className="bids-table">
              <thead>
                <tr>
                  <th>
                    <input
                      type="checkbox"
                      onChange={handleSelectAll}
                      checked={selectedBids.length === filteredBids.length && filteredBids.length > 0}
                    />
                  </th>
                  <th>Bid ID</th>
                  <th>Content Title</th>
                  <th>Bidder Name</th>
                  <th>Bid Amount</th>
                  <th>Status</th>
                  <th>Bid Date</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredBids.map((bid) => (
                  <tr key={bid.id}>
                    <td>
                      <input
                        type="checkbox"
                        checked={selectedBids.includes(bid.id)}
                        onChange={() => handleSelectBid(bid.id)}
                      />
                    </td>
                    <td>
                      <div className="bid-id">
                        <span className="bid-id-text">{bid.bidId}</span>
                        {bid.isAutoBid && <span className="auto-bid-badge">AUTO</span>}
                        {bid.isHighestBid && <span className="highest-bid-badge">HIGHEST</span>}
                      </div>
                    </td>
                    <td>
                      <div className="content-info">
                        <div className="content-thumbnail">
                          <FaGavel />
                        </div>
                        <div className="content-details">
                          <span className="content-title">{bid.contentTitle}</span>
                        </div>
                      </div>
                    </td>
                    <td>
                      <div className="bidder-info">
                        <span className="bidder-name">{bid.bidderName}</span>
                        <span className="bidder-email">{bid.bidderEmail}</span>
                      </div>
                    </td>
                    <td>
                      <div className="bid-amount">
                        <span className="amount">{formatCurrency(bid.bidAmount)}</span>
                        {bid.maxAutoBidAmount && (
                          <span className="max-auto-bid">
                            Max: {formatCurrency(bid.maxAutoBidAmount)}
                          </span>
                        )}
                      </div>
                    </td>
                    <td>
                      <span className={getStatusBadge(bid.status)}>
                        {bid.status}
                      </span>
                    </td>
                    <td>{formatDate(bid.bidDate)}</td>
                    <td>
                      <div className="table-actions">
                        <button
                          className="btn-action view"
                          title="View Bid Details"
                          onClick={() => handleBidAction(bid, 'view')}
                        >
                          <FaEye />
                        </button>
                        {bid.status === 'Active' && (
                          <>
                            <button
                              className="btn-action approve"
                              title="Approve Bid"
                              onClick={() => handleBidAction(bid, 'approve')}
                            >
                              <FaCheck />
                            </button>
                            <button
                              className="btn-action reject"
                              title="Reject Bid"
                              onClick={() => handleBidAction(bid, 'reject')}
                            >
                              <FaTimes />
                            </button>
                          </>
                        )}
                        <button
                          className="btn-action edit"
                          title="Edit Bid"
                          onClick={() => handleBidAction(bid, 'edit')}
                        >
                          <FaEdit />
                        </button>
                        <button
                          className="btn-action delete"
                          title="Delete Bid"
                          onClick={() => handleBidAction(bid, 'delete')}
                        >
                          <FaTrash />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredBids.length === 0 && (
            <div className="no-results">
              <FaGavel className="no-results-icon" />
              <h3>No bids found</h3>
              <p>Try adjusting your search or filter criteria</p>
            </div>
          )}
        </div>

        {/* Pagination */}
        <div className="AdminBidManagement__pagination">
          <div className="pagination-info">
            Showing {filteredBids.length} of {bids.length} bids
          </div>
          <div className="pagination-controls">
            <button className="btn btn-outline" disabled>Previous</button>
            <span className="page-number active">1</span>
            <button className="btn btn-outline" disabled>Next</button>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminBidManagement;
