/* Confirmation Modal Styles */
.confirmation-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1600;
  animation: fadeIn 0.2s ease-out;
}

.confirmation-modal {
  background: white;
  border-radius: 12px;
  padding: 0;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  animation: slideIn 0.3s ease-out;
}

.confirmation-modal-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 24px 24px 16px 24px;
  border-bottom: 1px solid #e5e7eb;
  position: relative;
}

.confirmation-modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  flex: 1;
}

.modal-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.modal-icon.danger {
  color: #dc2626;
}

.modal-icon.warning {
  color: #f59e0b;
}

.modal-icon.info {
  color: #3b82f6;
}

.close-button {
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #6b7280;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.close-button:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.close-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.confirmation-modal-content {
  padding: 16px 24px 24px 24px;
}

.confirmation-message {
  color: #4b5563;
  line-height: 1.6;
}

.confirmation-message p {
  margin: 0 0 12px 0;
}

.confirmation-message p:last-child {
  margin-bottom: 0;
}

.confirmation-message strong {
  color: #1f2937;
  font-weight: 600;
}

.warning-text {
  color: #f59e0b;
  font-size: 0.875rem;
  font-style: italic;
  margin-top: 8px;
}

.confirmation-modal-actions {
  display: flex;
  gap: 12px;
  padding: 16px 24px 24px 24px;
  justify-content: flex-end;
  border-top: 1px solid #e5e7eb;
}

/* Button Styles */
.btn {
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.875rem;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  min-width: 80px;
  justify-content: center;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-secondary {
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #e5e7eb;
  border-color: #9ca3af;
}

.btn-danger {
  background-color: #dc2626;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #b91c1c;
}

.btn-warning {
  background-color: #f59e0b;
  color: white;
}

.btn-warning:hover:not(:disabled) {
  background-color: #d97706;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #2563eb;
}

/* Spinner */
.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .confirmation-modal {
    width: 95%;
    margin: 20px;
  }
  
  .confirmation-modal-header {
    padding: 20px 20px 12px 20px;
  }
  
  .confirmation-modal-content {
    padding: 12px 20px 20px 20px;
  }
  
  .confirmation-modal-actions {
    padding: 12px 20px 20px 20px;
    flex-direction: column-reverse;
  }
  
  .btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .confirmation-modal-header h3 {
    font-size: 1.125rem;
  }
  
  .modal-icon {
    font-size: 1.25rem;
  }
}
