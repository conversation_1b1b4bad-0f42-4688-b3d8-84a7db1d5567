import React, { useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectCurrentApprovalItem,
  selectLoading,
  hideApprovalModal,
  setApprovalLoading,
  addActivity,
} from "../../redux/slices/adminDashboardSlice";
import {
  approveContent,
  rejectContent,
} from "../../redux/slices/adminDashboardThunks";
import "../../styles/ApprovalModal.css";

// Icons
import { FaTimes, FaCheck, FaVideo, FaUser, FaCalendarAlt, FaDollarSign } from "react-icons/fa";
import { MdCategory } from "react-icons/md";

const ApprovalModal = () => {
  const dispatch = useDispatch();
  const currentItem = useSelector(selectCurrentApprovalItem);
  const loading = useSelector(selectLoading);
  const [approvalReason, setApprovalReason] = useState("");
  const [rejectionReason, setRejectionReason] = useState("");
  const [actionType, setActionType] = useState(null); // 'approve' or 'reject'

  if (!currentItem) return null;

  const handleClose = () => {
    dispatch(hideApprovalModal());
    setApprovalReason("");
    setRejectionReason("");
    setActionType(null);
  };

  const handleApprove = async () => {
    if (!approvalReason.trim()) {
      alert("Please provide an approval reason");
      return;
    }

    try {
      dispatch(setApprovalLoading(true));

      // Dispatch the async thunk
      await dispatch(approveContent({
        id: currentItem.id,
        approvalNotes: approvalReason
      })).unwrap();

      dispatch(addActivity({
        id: Date.now(),
        type: 'content_approval',
        description: `Content approved: ${currentItem.contentTitle}`,
        timestamp: new Date().toISOString(),
        user: 'Admin',
      }));

      handleClose();

      // Show success message
      alert(`Content "${currentItem.contentTitle}" has been approved successfully!`);
    } catch (error) {
      console.error('Failed to approve content:', error);
      alert('Failed to approve content. Please try again.');
    } finally {
      dispatch(setApprovalLoading(false));
    }
  };

  const handleReject = async () => {
    if (!rejectionReason.trim()) {
      alert("Please provide a rejection reason");
      return;
    }

    try {
      dispatch(setApprovalLoading(true));

      // Dispatch the async thunk
      await dispatch(rejectContent({
        id: currentItem.id,
        reason: rejectionReason,
        rejectionNotes: rejectionReason
      })).unwrap();

      dispatch(addActivity({
        id: Date.now(),
        type: 'content_rejection',
        description: `Content rejected: ${currentItem.contentTitle}`,
        timestamp: new Date().toISOString(),
        user: 'Admin',
      }));

      handleClose();

      // Show success message
      alert(`Content "${currentItem.contentTitle}" has been rejected.`);
    } catch (error) {
      console.error('Failed to reject content:', error);
      alert('Failed to reject content. Please try again.');
    } finally {
      dispatch(setApprovalLoading(false));
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <div className="ApprovalModal">
      <div className="ApprovalModal__overlay" onClick={handleClose} />
      <div className="ApprovalModal__container">
        {/* Header */}
        <div className="ApprovalModal__header">
          <h2>Content Approval Review</h2>
          <button className="close-btn" onClick={handleClose}>
            <FaTimes />
          </button>
        </div>

        {/* Content Preview */}
        <div className="ApprovalModal__content">
          <div className="content-preview">
            <div className="content-thumbnail">
              {currentItem.thumbnail ? (
                <img src={currentItem.thumbnail} alt={currentItem.contentTitle} />
              ) : (
                <FaVideo />
              )}
            </div>
            <div className="content-details">
              <h3>{currentItem.contentTitle}</h3>
              <div className="content-meta">
                <div className="meta-item">
                  <FaUser className="meta-icon" />
                  <span>Seller: {currentItem.seller}</span>
                </div>
                <div className="meta-item">
                  <MdCategory className="meta-icon" />
                  <span>Category: {currentItem.category}</span>
                </div>
                <div className="meta-item">
                  <FaCalendarAlt className="meta-icon" />
                  <span>Submitted: {formatDate(currentItem.submissionDate)}</span>
                </div>
                <div className="meta-item">
                  <FaDollarSign className="meta-icon" />
                  <span>Price: $29.99</span>
                </div>
              </div>
            </div>
          </div>

          {/* Content Description */}
          <div className="content-description">
            <h4>Content Description</h4>
            <p>
              This comprehensive training content covers advanced techniques and strategies
              for {currentItem.category.toLowerCase()}. The content includes detailed explanations,
              practical examples, and step-by-step guidance to help users improve their skills.
            </p>
          </div>

          {/* Seller Information */}
          <div className="seller-info">
            <h4>Seller Information</h4>
            <div className="seller-details">
              <div className="seller-avatar">
                <FaUser />
              </div>
              <div className="seller-data">
                <p><strong>Name:</strong> {currentItem.seller}</p>
                <p><strong>Rating:</strong> 4.8/5 (124 reviews)</p>
                <p><strong>Total Content:</strong> 23 items</p>
                <p><strong>Member Since:</strong> January 2023</p>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="approval-actions">
            {!actionType && (
              <div className="action-buttons">
                <button
                  className="btn btn-approve"
                  onClick={() => setActionType('approve')}
                  disabled={loading.approval}
                >
                  <FaCheck />
                  Approve Content
                </button>
                <button
                  className="btn btn-reject"
                  onClick={() => setActionType('reject')}
                  disabled={loading.approval}
                >
                  <FaTimes />
                  Reject Content
                </button>
              </div>
            )}

            {actionType === 'approve' && (
              <div className="approval-form">
                <h4>Approve Content</h4>
                <div className="form-group">
                  <label>Approval Reason/Notes</label>
                  <textarea
                    value={approvalReason}
                    onChange={(e) => setApprovalReason(e.target.value)}
                    placeholder="Provide reason for approval and any notes for the seller..."
                    rows="4"
                    className="form-textarea"
                  />
                </div>
                <div className="form-actions">
                  <button
                    className="btn btn-approve"
                    onClick={handleApprove}
                    disabled={loading.approval || !approvalReason.trim()}
                  >
                    {loading.approval ? "Approving..." : "Confirm Approval"}
                  </button>
                  <button
                    className="btn btn-outline"
                    onClick={() => setActionType(null)}
                    disabled={loading.approval}
                  >
                    Cancel
                  </button>
                </div>
              </div>
            )}

            {actionType === 'reject' && (
              <div className="rejection-form">
                <h4>Reject Content</h4>
                <div className="form-group">
                  <label>Rejection Reason <span className="required">*</span></label>
                  <textarea
                    value={rejectionReason}
                    onChange={(e) => setRejectionReason(e.target.value)}
                    placeholder="Provide detailed reason for rejection. This will be sent to the seller..."
                    rows="4"
                    className="form-textarea"
                  />
                </div>
                <div className="form-actions">
                  <button
                    className="btn btn-reject"
                    onClick={handleReject}
                    disabled={loading.approval || !rejectionReason.trim()}
                  >
                    {loading.approval ? "Rejecting..." : "Confirm Rejection"}
                  </button>
                  <button
                    className="btn btn-outline"
                    onClick={() => setActionType(null)}
                    disabled={loading.approval}
                  >
                    Cancel
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApprovalModal;
