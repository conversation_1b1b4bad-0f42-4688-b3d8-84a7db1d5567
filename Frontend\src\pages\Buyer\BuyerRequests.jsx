import React from "react";
import { useSelector } from "react-redux";
import { selectMyRequests } from "../../redux/slices/buyerDashboardSlice";
import SectionWrapper from "../../components/common/SectionWrapper";
import { FaEye } from "react-icons/fa";
import Table from "../../components/common/Table";
import "../../styles/BuyerRequests.css";
import { MdRequestPage } from "react-icons/md";

const BuyerRequests = () => {
  const requests = useSelector(selectMyRequests);

  const columns = [
    {
      key: "no",
      label: "No.",
      className: "no",
    },
    {
      key: "requestId",
      label: "Request Id",
      className: "request-id",
    },
    {
      key: "video",
      label: "Videos/Documents",
      className: "video",
    },
    {
      key: "date",
      label: "Date",
      className: "date",
    },
    {
      key: "requestedAmount",
      label: "Requested Amount",
      className: "requested-amount",
    },
    {
      key: "status",
      label: "Status",
      className: "status",
    },
    {
      key: "action",
      label: "Action",
      className: "action",
    },
  ];

  const renderRow = (request, index) => (
    <>
      <div className="table-cell no">{index + 1}</div>
      <div className="table-cell request-id">#REQUEST{request.id}</div>
      <div className="table-cell video">
        <div className="content-item">
          <div className="content-image">
            <img
              src="https://images.unsplash.com/photo-1566577739112-5180d4bf9390?q=80&w=300&h=200&auto=format&fit=crop"
              alt={request.title}
            />
          </div>
          <div className="content-info">
            <div className="content-title">{request.title}</div>
            <div className="content-coach">By Coach</div>
          </div>
        </div>
      </div>
      <div className="table-cell date">{request.date} | 4:30PM</div>
      <div className="table-cell requested-amount">$22.00</div>
      <div className="table-cell status">
        <span className={`status-badge ${request.status}`}>
          {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
        </span>
      </div>
      <div className="table-cell action">
        <button className="action-btn">
          <FaEye />
        </button>
      </div>
    </>
  );

  return (
    <div className="BuyerRequests">
      <SectionWrapper
        icon={<MdRequestPage className="BuyerSidebar__icon" />}
        title="My Requests"
      >
        {requests.length > 0 ? (
          <Table
            columns={columns}
            data={requests}
            renderRow={renderRow}
            variant="grid"
            className="BuyerRequests__table"
            emptyMessage="You have no requests yet."
          />
        ) : (
          <div className="BuyerRequests__empty">
            <p>You have no requests yet.</p>
          </div>
        )}
      </SectionWrapper>
    </div>
  );
};

export default BuyerRequests;
