import { toast } from 'react-toastify';
import authService from '../services/authService';

/**
 * Navigation utility functions for handling authentication and role-based access
 */

/**
 * Check if user is authenticated
 * @returns {boolean} True if user is authenticated
 */
export const isUserAuthenticated = () => {
  return authService.isAuthenticated();
};

/**
 * Get current user data
 * @returns {Object|null} User object or null
 */
export const getCurrentUser = () => {
  return authService.getStoredUser();
};

/**
 * Get user role
 * @returns {string} User role or 'visitor'
 */
export const getUserRole = () => {
  const user = getCurrentUser();
  return user?.role || 'visitor';
};

/**
 * Check if user can access buyer routes
 * @returns {boolean} True if user can access buyer routes
 */
export const canAccessBuyerRoutes = () => {
  const user = getCurrentUser();
  return isUserAuthenticated() && user?.role === 'buyer';
};

/**
 * Check if user can access seller routes
 * @returns {boolean} True if user can access seller routes
 */
export const canAccessSellerRoutes = () => {
  const user = getCurrentUser();
  return isUserAuthenticated() && user?.role === 'seller';
};

/**
 * Handle Buy tab navigation with authentication and role checks
 * @param {Function} navigate - React Router navigate function
 * @returns {boolean} True if navigation was successful
 */
export const handleBuyNavigation = (navigate) => {
  if (!isUserAuthenticated()) {
    toast.error('Please sign in to access buyer features');
    navigate('/auth');
    return false;
  }

  const userRole = getUserRole();
  
  if (userRole === 'buyer') {
    navigate('/buyer/account/dashboard');
    return true;
  } else if (userRole === 'seller') {
    toast.error('Access denied. Sellers cannot access buyer features.');
    return false;
  } else if (userRole === 'admin') {
    toast.info('Admin users can access all features');
    navigate('/buyer/dashboard');
    return true;
  }
  
  toast.error('Unauthorized access');
  return false;
};

/**
 * Handle Sell tab navigation with authentication and role checks
 * @param {Function} navigate - React Router navigate function
 * @returns {boolean} True if navigation was successful
 */
export const handleSellNavigation = (navigate) => {
  if (!isUserAuthenticated()) {
    toast.error('Please sign in to access seller features');
    navigate('/auth');
    return false;
  }

  const userRole = getUserRole();
  
  if (userRole === 'seller') {
    navigate('/seller/dashboard');
    return true;
  } else if (userRole === 'buyer') {
    toast.error('Access denied. Buyers cannot access seller features.');
    return false;
  } else if (userRole === 'admin') {
    toast.info('Admin users can access all features');
    navigate('/seller/dashboard');
    return true;
  }
  
  toast.error('Unauthorized access');
  return false;
};

/**
 * Get appropriate dashboard route for user role
 * @returns {string} Dashboard route path
 */
export const getUserDashboardRoute = () => {
  const userRole = getUserRole();
  
  switch (userRole) {
    case 'buyer':
      return '/buyer/account/dashboard';
    case 'seller':
      return '/seller/dashboard';
    case 'admin':
      return '/admin/dashboard';
    default:
      return '/';
  }
};

/**
 * Check if current route matches user's role
 * @param {string} pathname - Current route pathname
 * @returns {boolean} True if route is appropriate for user role
 */
export const isRouteAuthorizedForUser = (pathname) => {
  const userRole = getUserRole();
  
  if (!isUserAuthenticated()) {
    return true; // Allow public routes for non-authenticated users
  }
  
  if (userRole === 'admin') {
    return true; // Admin can access all routes
  }
  
  if (pathname.startsWith('/buyer/') && userRole !== 'buyer') {
    return false;
  }
  
  if (pathname.startsWith('/seller/') && userRole !== 'seller') {
    return false;
  }
  
  return true;
};

/**
 * Handle unauthorized route access
 * @param {string} pathname - Attempted route pathname
 * @param {Function} navigate - React Router navigate function
 */
export const handleUnauthorizedAccess = (pathname, navigate) => {
  const userRole = getUserRole();
  
  if (pathname.startsWith('/buyer/') && userRole === 'seller') {
    toast.error('Access denied. Sellers cannot access buyer features.');
    navigate('/seller/dashboard');
  } else if (pathname.startsWith('/seller/') && userRole === 'buyer') {
    toast.error('Access denied. Buyers cannot access seller features.');
    navigate('/buyer/account/dashboard');
  } else {
    toast.error('Unauthorized access');
    navigate(getUserDashboardRoute());
  }
};
